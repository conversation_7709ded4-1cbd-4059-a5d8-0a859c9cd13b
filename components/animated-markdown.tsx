"use client";

import { motion } from "framer-motion";
import { memo, useEffect, useRef, useState } from "react";
import { Markdown } from "./markdown";

interface AnimatedMarkdownProps {
  children: string;
  isStreaming?: boolean;
}

const AnimatedMarkdown = ({ children, isStreaming = false }: AnimatedMarkdownProps) => {
  const [displayedText, setDisplayedText] = useState("");
  const [isAnimating, setIsAnimating] = useState(false);
  const previousTextRef = useRef("");
  const animationTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!isStreaming) {
      setDisplayedText(children);
      setIsAnimating(false);
      return;
    }

    const previousText = previousTextRef.current;

    // Check if new content was added
    if (children.length > previousText.length && children.startsWith(previousText)) {
      const newContent = children.slice(previousText.length);

      if (newContent.length > 0) {
        setIsAnimating(true);

        // Clear any existing animation
        if (animationTimeoutRef.current) {
          clearTimeout(animationTimeoutRef.current);
        }

        // Animate new content character by character with word-aware timing
        let currentIndex = 0;
        const animateNextChar = () => {
          if (currentIndex < newContent.length) {
            const char = newContent[currentIndex];
            setDisplayedText(prev => prev + char);
            currentIndex++;

            // Vary timing based on character type for more natural feel
            let delay = 30; // Base delay
            if (char === ' ') delay = 60; // Longer pause for spaces
            if (char === '.' || char === '!' || char === '?') delay = 200; // Longer pause for sentence endings
            if (char === ',' || char === ';') delay = 100; // Medium pause for commas

            animationTimeoutRef.current = setTimeout(animateNextChar, delay);
          } else {
            setIsAnimating(false);
          }
        };

        animateNextChar();
      }
    } else if (children !== previousText) {
      // Text was reset (new message), reset everything
      setDisplayedText("");
      setIsAnimating(false);
    }

    previousTextRef.current = children;

    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, [children, isStreaming]);

  // If not streaming, render normal markdown
  if (!isStreaming) {
    return <Markdown>{children}</Markdown>;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2 }}
      className="relative"
    >
      <motion.div
        key={displayedText.length} // Re-trigger animation when text changes
        initial={{ y: 1 }}
        animate={{ y: 0 }}
        transition={{
          duration: 0.2,
          ease: "easeOut"
        }}
      >
        <Markdown>{displayedText}</Markdown>
      </motion.div>

      {/* Typing cursor effect - only show when actively streaming */}
      {isAnimating && (
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: [0, 1, 0] }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="inline-block w-0.5 h-4 bg-current ml-1 align-text-bottom"
        />
      )}
    </motion.div>
  );
};

export default memo(AnimatedMarkdown);
