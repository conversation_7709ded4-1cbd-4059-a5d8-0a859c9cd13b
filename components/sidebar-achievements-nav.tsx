"use client";

import { useState } from "react";
import { TrophyIcon } from "@/components/icons";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { AchievementsDialog } from "@/components/achievements-dialog";
import { useAchievements } from "@/hooks/use-achievements";
import { AnimatedCounter } from "@/components/animated-counter";

export function SidebarAchievementsNav() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { count, isLoading } = useAchievements();

  return (
    <>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton
            onClick={() => setIsDialogOpen(true)}
            className="w-full justify-start"
          >
            <TrophyIcon size={16} />
            <span>
              Achievements (
              {isLoading ? "..." : <AnimatedCounter value={count} />})
            </span>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>

      <AchievementsDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} />
    </>
  );
}
