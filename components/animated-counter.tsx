"use client";

import { useEffect, useRef, useState } from "react";
import {
  AnimatePresence,
  motion,
  useSpring,
  useTransform,
} from "framer-motion";

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  className?: string;
}

export function AnimatedCounter({
  value,
  duration = 0.8,
  className = "",
}: AnimatedCounterProps) {
  const [displayValue, setDisplayValue] = useState(value);
  const prevValueRef = useRef(value);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isIncreasing, setIsIncreasing] = useState(false);

  // Spring animation for smooth counting
  const spring = useSpring(value, {
    duration: duration * 1000,
    bounce: 0.1,
  });

  // Transform the spring value to an integer for display
  const rounded = useTransform(spring, (latest) => Math.round(latest));

  useEffect(() => {
    const unsubscribe = rounded.on("change", (latest) => {
      setDisplayValue(latest);
    });

    return unsubscribe;
  }, [rounded]);

  useEffect(() => {
    if (prevValueRef.current !== value) {
      setIsAnimating(true);
      setIsIncreasing(value > prevValueRef.current);
      spring.set(value);

      // Reset animation state after duration
      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, duration * 1000);

      prevValueRef.current = value;

      return () => clearTimeout(timer);
    }
  }, [value, spring, duration]);

  return (
    <motion.span
      className={`inline-block ${className}`}
      animate={
        isAnimating
          ? {
              scale: [1, 1.15, 1],
              y: [0, -1, 0],
            }
          : {}
      }
      transition={{
        duration: 0.4,
        ease: [0.4, 0, 0.6, 1],
      }}
    >
      <AnimatePresence mode="wait">
        <motion.span
          key={`${displayValue}-${isAnimating}`}
          initial={
            isAnimating
              ? {
                  opacity: 0,
                  y: isIncreasing ? 10 : -10,
                  scale: 0.8,
                }
              : false
          }
          animate={{
            opacity: 1,
            y: 0,
            scale: 1,
            color: isAnimating
              ? isIncreasing
                ? "#10b981"
                : "#f59e0b"
              : "currentColor",
          }}
          exit={{
            opacity: 0,
            y: isIncreasing ? -10 : 10,
            scale: 0.8,
          }}
          transition={{
            duration: 0.3,
            ease: "easeOut",
          }}
          className="inline-block"
        >
          {displayValue}
        </motion.span>
      </AnimatePresence>
    </motion.span>
  );
}
